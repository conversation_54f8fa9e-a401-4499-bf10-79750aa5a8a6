#!/usr/bin/env python3
"""
客户数据导入脚本
从Excel文件导入客户信息到系统中
"""

import asyncio
import os
import sys
import pandas as pd
import re
from datetime import datetime
from typing import Dict, List, Optional, Set

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.ext.asyncio import AsyncSession
from config.database import AsyncSessionLocal
from config.get_db import get_db
from module_admin.entity.do.user_do import SysUser
from module_customer.entity.do.customer_do import Customer, CustomerContact, CustomerInternalManager
from module_customer.entity.do.customer_follow_record_do import CustomerFollowRecord
from utils.log_util import logger


class CustomerImporter:
    """客户数据导入器"""

    def __init__(self, excel_file_path: str):
        self.excel_file_path = excel_file_path
        self.df = None
        self.existing_users: Dict[str, int] = {}  # 用户名 -> user_id
        self.created_users: Dict[str, int] = {}   # 新创建的用户
        self.existing_customers: Dict[str, int] = {}  # 客户名称 -> customer_id

    async def load_excel_data(self):
        """加载Excel数据"""
        try:
            self.df = pd.read_excel(self.excel_file_path)
            # 替换NaN值为None
            self.df = self.df.where(pd.notnull(self.df), None)
            logger.info(f"成功加载Excel文件，共{len(self.df)}行数据")
            return True
        except Exception as e:
            logger.error(f"加载Excel文件失败: {e}")
            return False

    async def load_existing_users(self, db: AsyncSession):
        """加载现有用户"""
        try:
            from sqlalchemy import text
            result = await db.execute(
                text("SELECT user_id, nick_name FROM sys_user WHERE del_flag = '0'")
            )
            users = result.fetchall()
            for user in users:
                self.existing_users[user.nick_name] = user.user_id
            logger.info(f"加载了{len(self.existing_users)}个现有用户")
        except Exception as e:
            logger.error(f"加载现有用户失败: {e}")

    async def load_existing_customers(self, db: AsyncSession):
        """加载现有客户"""
        try:
            from sqlalchemy import text
            result = await db.execute(
                text("SELECT customer_id, customer_name FROM customer WHERE del_flag = '0'")
            )
            customers = result.fetchall()
            for customer in customers:
                self.existing_customers[customer.customer_name] = customer.customer_id
            logger.info(f"加载了{len(self.existing_customers)}个现有客户")
        except Exception as e:
            logger.error(f"加载现有客户失败: {e}")

    def extract_unique_managers(self) -> Set[str]:
        """提取所有唯一的负责人姓名"""
        managers = set()
        for _, row in self.df.iterrows():
            manager = row.get('负责人')
            if manager and pd.notna(manager) and manager.strip():
                managers.add(manager.strip())
        return managers

    async def create_missing_users(self, db: AsyncSession, managers: Set[str]):
        """创建缺失的用户"""
        for manager in managers:
            if manager not in self.existing_users:
                try:
                    # 直接使用SQL插入用户，避免复杂的服务层调用
                    from sqlalchemy import text
                    import hashlib

                    # 简单的密码加密（使用MD5，与系统保持一致）
                    password_hash = hashlib.md5("123456".encode()).hexdigest()

                    # 检查用户名是否已存在
                    check_result = await db.execute(
                        text("SELECT user_id FROM sys_user WHERE user_name = :user_name OR nick_name = :nick_name"),
                        {"user_name": manager, "nick_name": manager}
                    )
                    existing = check_result.fetchone()

                    if existing:
                        self.existing_users[manager] = existing.user_id
                        logger.info(f"找到现有用户: {manager}")
                        continue

                    # 插入新用户
                    insert_sql = text("""
                        INSERT INTO sys_user (
                            user_name, nick_name, password, email, phonenumber,
                            sex, status, del_flag, create_by, create_time,
                            update_by, update_time
                        ) VALUES (
                            :user_name, :nick_name, :password, :email, :phonenumber,
                            :sex, :status, :del_flag, :create_by, :create_time,
                            :update_by, :update_time
                        )
                    """)

                    await db.execute(insert_sql, {
                        "user_name": manager,
                        "nick_name": manager,
                        "password": password_hash,
                        "email": f"{manager}@company.com",
                        "phonenumber": "",
                        "sex": "0",
                        "status": "0",
                        "del_flag": "0",
                        "create_by": "system",
                        "create_time": datetime.now(),
                        "update_by": "system",
                        "update_time": datetime.now()
                    })

                    # 获取新创建的用户ID
                    user_id_result = await db.execute(
                        text("SELECT user_id FROM sys_user WHERE user_name = :user_name"),
                        {"user_name": manager}
                    )
                    new_user = user_id_result.fetchone()

                    if new_user:
                        self.created_users[manager] = new_user.user_id
                        self.existing_users[manager] = new_user.user_id
                        logger.info(f"成功创建用户: {manager} (ID: {new_user.user_id})")
                    else:
                        logger.error(f"创建用户后无法找到用户: {manager}")

                except Exception as e:
                    logger.error(f"创建用户{manager}时发生异常: {e}")
                    # 尝试查找是否已存在
                    try:
                        from sqlalchemy import text
                        check_result = await db.execute(
                            text("SELECT user_id FROM sys_user WHERE nick_name = :nick_name"),
                            {"nick_name": manager}
                        )
                        existing = check_result.fetchone()
                        if existing:
                            self.existing_users[manager] = existing.user_id
                            logger.info(f"找到现有用户: {manager}")
                    except Exception as e2:
                        logger.error(f"查找用户{manager}时也发生异常: {e2}")

    def parse_address(self, address_str: str) -> tuple:
        """解析地址字符串，返回(省份, 城市, 区县)"""
        if not address_str or pd.isna(address_str):
            return None, None, None

        # 处理格式如"浙江省/杭州市/滨江区"
        if '/' in address_str:
            parts = address_str.split('/')
            province = parts[0].replace('省', '') if len(parts) > 0 else None
            city = parts[1].replace('市', '') if len(parts) > 1 else None
            district = parts[2].replace('区', '').replace('县', '') if len(parts) > 2 else None
            return province, city, district

        # 简单的地址解析
        province_match = re.search(r'(.*?省)', address_str)
        city_match = re.search(r'(.*?市)', address_str)
        district_match = re.search(r'(.*?[区县])', address_str)

        province = province_match.group(1).replace('省', '') if province_match else None
        city = city_match.group(1).replace('市', '') if city_match else None
        district = district_match.group(1).replace('区', '').replace('县', '') if district_match else None

        return province, city, district

    async def create_customer_group(self, db: AsyncSession, group_name: str, manager_name: str) -> Optional[int]:
        """创建客户集团"""
        if not group_name or group_name == '无' or pd.isna(group_name):
            return None

        # 检查是否已存在
        if group_name in self.existing_customers:
            return self.existing_customers[group_name]

        try:
            manager_id = self.existing_users.get(manager_name)

            customer_group = Customer(
                customer_name=group_name,
                customer_level='1',  # 集团
                parent_id=None,
                customer_type='企业',  # 默认为企业
                customer_source='导入',
                province='',
                city='',
                district=None,
                address=None,
                status='0',
                del_flag='0',
                create_by='system',
                create_time=datetime.now(),
                update_by='system',
                update_time=datetime.now(),
                remark='Excel导入',
                customer_importance=None
            )

            db.add(customer_group)
            await db.flush()

            # 添加内部负责人关联
            if manager_id:
                manager_relation = CustomerInternalManager(
                    customer_id=customer_group.customer_id,
                    user_id=manager_id,
                    is_primary='1',
                    create_by='system',
                    create_time=datetime.now(),
                    update_by='system',
                    update_time=datetime.now()
                )
                db.add(manager_relation)

            self.existing_customers[group_name] = customer_group.customer_id
            logger.info(f"成功创建客户集团: {group_name}")
            return customer_group.customer_id

        except Exception as e:
            logger.error(f"创建客户集团{group_name}失败: {e}")
            return None

    async def create_customer_company(self, db: AsyncSession, row) -> Optional[int]:
        """创建客户公司"""
        company_name = row.get('公司名称')
        if not company_name or pd.isna(company_name):
            return None

        # 检查是否已存在
        if company_name in self.existing_customers:
            logger.info(f"客户公司已存在: {company_name}")
            return self.existing_customers[company_name]

        try:
            # 获取集团ID
            group_name = row.get('客户集团')
            parent_id = None
            if group_name and group_name != '无' and not pd.isna(group_name):
                parent_id = self.existing_customers.get(group_name)

            # 解析地址
            address_str = row.get('地区')
            detail_address = row.get('详细地址')
            province, city, district = self.parse_address(address_str)

            # 获取负责人ID
            manager_name = row.get('负责人')
            manager_id = self.existing_users.get(manager_name) if manager_name else None

            # 映射客户类型
            customer_type = row.get('客户类别', '企业')
            if pd.isna(customer_type):
                customer_type = '企业'

            # 映射客户来源
            customer_source = row.get('来源', '导入')
            if pd.isna(customer_source):
                customer_source = '导入'

            # 映射客户重要性
            customer_importance = row.get('客户级别')
            if pd.isna(customer_importance):
                customer_importance = None

            # 处理备注字段的NaN值
            remark = row.get('备注')
            if pd.isna(remark):
                remark = None

            customer_company = Customer(
                customer_name=company_name,
                customer_level='2',  # 公司
                parent_id=parent_id,
                customer_type=customer_type,
                customer_source=customer_source,
                province=province or '',
                city=city or '',
                district=district,
                address=detail_address,
                status='0',
                del_flag='0',
                create_by='system',
                create_time=datetime.now(),
                update_by='system',
                update_time=datetime.now(),
                remark=remark,
                customer_importance=customer_importance
            )

            db.add(customer_company)
            await db.flush()

            # 添加内部负责人关联
            if manager_id:
                manager_relation = CustomerInternalManager(
                    customer_id=customer_company.customer_id,
                    user_id=manager_id,
                    is_primary='1',
                    create_by='system',
                    create_time=datetime.now(),
                    update_by='system',
                    update_time=datetime.now()
                )
                db.add(manager_relation)

            self.existing_customers[company_name] = customer_company.customer_id
            logger.info(f"成功创建客户公司: {company_name}")
            return customer_company.customer_id

        except Exception as e:
            logger.error(f"创建客户公司{company_name}失败: {e}")
            return None

    async def create_customer_contacts(self, db: AsyncSession, customer_id: int, contacts_str: str):
        """创建客户联系人"""
        if not contacts_str or pd.isna(contacts_str):
            return

        # 解析联系人字符串，支持逗号和中文逗号分隔
        contacts = re.split(r'[,，]', contacts_str)

        for i, contact_name in enumerate(contacts):
            contact_name = contact_name.strip()
            if not contact_name:
                continue

            try:
                contact = CustomerContact(
                    customer_id=customer_id,
                    contact_name=contact_name,
                    position=None,
                    phone=None,
                    wechat=None,
                    email=None,
                    is_primary='1' if i == 0 else '0',  # 第一个联系人设为主要联系人
                    status='0',
                    create_by='system',
                    create_time=datetime.now(),
                    update_by='system',
                    update_time=datetime.now(),
                    remark='Excel导入'
                )

                db.add(contact)
                logger.info(f"成功创建客户联系人: {contact_name}")

            except Exception as e:
                logger.error(f"创建客户联系人{contact_name}失败: {e}")

    async def import_data(self):
        """执行数据导入"""
        if not await self.load_excel_data():
            return False

        async with AsyncSessionLocal() as db:
            try:
                # 加载现有数据
                await self.load_existing_users(db)
                await self.load_existing_customers(db)

                # 提取并创建缺失的用户
                managers = self.extract_unique_managers()
                logger.info(f"发现{len(managers)}个负责人: {managers}")
                await self.create_missing_users(db, managers)

                # 首先创建所有集团
                groups_created = set()
                for _, row in self.df.iterrows():
                    group_name = row.get('客户集团')
                    manager_name = row.get('负责人')

                    if group_name and group_name != '无' and not pd.isna(group_name) and group_name not in groups_created:
                        await self.create_customer_group(db, group_name, manager_name)
                        groups_created.add(group_name)

                # 然后创建所有公司
                for _, row in self.df.iterrows():
                    customer_id = await self.create_customer_company(db, row)

                    # 创建客户联系人
                    if customer_id:
                        contacts_str = row.get('客户联系人')
                        await self.create_customer_contacts(db, customer_id, contacts_str)

                await db.commit()
                logger.info("数据导入完成！")
                return True

            except Exception as e:
                await db.rollback()
                logger.error(f"数据导入失败: {e}")
                return False


async def main():
    """主函数"""
    excel_file = "docs/客户示例.xlsx"

    if not os.path.exists(excel_file):
        logger.error(f"Excel文件不存在: {excel_file}")
        return

    importer = CustomerImporter(excel_file)
    success = await importer.import_data()

    if success:
        print("✅ 客户数据导入成功！")
        print(f"📊 创建了 {len(importer.created_users)} 个新用户")
        print(f"📊 处理了 {len(importer.df)} 条客户记录")
    else:
        print("❌ 客户数据导入失败！")


if __name__ == "__main__":
    asyncio.run(main())